-- Insert tenant account join data into tenant_account_joins table (PostgreSQL compatible)
INSERT INTO tenant_account_joins (
    id,
    tenant_id,
    account_id,
    role,
    invited_by,
    created_at,
    updated_at,
    "current"
) VALUES (
    '287160b7-3718-4bdd-ad9f-68e1282abcda'::uuid,
    '4387590f-c8ee-4dc4-a649-a57d1bd0b8ac'::uuid,
    '7c10ae05-3a3e-4f62-9e7c-d1c2b1f1b687'::uuid,
    'owner',
    NULL,
    '2025-03-13 10:24:23'::timestamp,
    '2025-03-13 10:24:23'::timestamp,
    true
);
