-- Insert tenant data into tenants table (PostgreSQL compatible)
INSERT INTO tenants (
    id,
    name,
    encrypt_public_key,
    plan,
    status,
    created_at,
    updated_at,
    custom_config
) VALUES (
    '4387590f-c8ee-4dc4-a649-a57d1bd0b8ac'::uuid,
    '<EMAIL>''s Workspace',
    '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7iRpMGYXjo/5MFJoR02z
KGmcZ+RwaGe6ZgQWAO52SB6Zq1ToWYXlmJQUbDIdIIvP/mN52QJAHepeH+b9xvJq
NR8amgcZhhldMZDizoDM8tWOXZQdCauHv7LrJD0y6AhbB1LUL5317genDW1qdE9B
vCRYS3CV5wieKaEt6CTUl4tgSJfd8nWnB1fgmUiDdIEkGSPzYOYOWNnhpVo/NoGw
r0zYcsLy+ZQylLuQsmtjxsRpRsZDHbnz8VribtydRTESd6E/99CU+szGy1BdOs0/
hjUxpfBmsFGHkO6n/h+bZgqZmz2ferx5JVh+OgFLsvo2ykKjWwa93GjiJst3f0yi
lQIDAQAB
-----END PUBLIC KEY-----',
    'basic',
    'normal',
    '2025-03-13 10:24:23'::timestamp,
    '2025-03-13 10:24:23'::timestamp,
    NULL
);
