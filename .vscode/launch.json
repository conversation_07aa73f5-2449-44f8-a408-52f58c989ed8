{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Celery",
            "type": "debugpy",
            "request": "launch",
            "module": "celery",
            "justMyCode": true,
            "args": ["-A", "app.celery", "worker", "-P", "gevent", "-c", "1", "--loglevel", "info", "-Q", "dataset,generation,mail"],
            "envFile": "${workspaceFolder}/.env",
            "env": {
                "FLASK_APP": "app.py",
                "FLASK_DEBUG": "1",
                "GEVENT_SUPPORT": "True",
                // "ORACLE_HOME": "/home/<USER>/instantclient_23_4",
                // "LD_LIBRARY_PATH": "/home/<USER>/instantclient_23_4",
                // "TNS_ADMIN": "/home/<USER>/instantclient_23_4/network/admin",
            },
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/api",
        },
        {
            "name": "api",
            "consoleName": "api",
            "type": "debugpy",
            "request": "launch",
            // "python": "${workspaceFolder}/.venv/bin/python",
            "cwd": "${workspaceFolder}/api",
            "envFile": ".env",
            "module": "flask",
            "justMyCode": true,
            "jinja": true,
            "env": {
                "FLASK_APP": "app.py",
                "GEVENT_SUPPORT": "True",
            },
            "args": [
                "run",
                "--port=5001"
            ]
        },
        {
            "name": "Python: Flask",
            "type": "debugpy",
            "request": "launch",
            "module": "flask",
            "env": {
                "FLASK_APP": "app.py",
                "FLASK_DEBUG": "1",
                "GEVENT_SUPPORT": "True",
                "ORACLE_HOME": "/home/<USER>/instantclient_23_4",
                "LD_LIBRARY_PATH": "/home/<USER>/instantclient_23_4",
                "TNS_ADMIN": "/home/<USER>/instantclient_23_4/network/admin",
            },
            "args": [
                "run",
                "--host=0.0.0.0",
                "--port=5001",
            ],
            "jinja": true,
            "justMyCode": true,
            "cwd": "${workspaceFolder}/api"
        }
    ]
}